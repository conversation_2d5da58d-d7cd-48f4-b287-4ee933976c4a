namespace ProManage.Forms
{
    partial class PermissionManagementForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControlMain = new DevExpress.XtraTab.XtraTabControl();
            this.tabPageRolePermissions = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlRolePermissions = new DevExpress.XtraGrid.GridControl();
            this.gridViewRolePermissions = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.panelRoleSelection = new DevExpress.XtraEditors.PanelControl();
            this.cmbRoles = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.tabPageUserPermissions = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlUserPermissions = new DevExpress.XtraGrid.GridControl();
            this.gridViewUserPermissions = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.panelUserSelection = new DevExpress.XtraEditors.PanelControl();
            this.cmbUsers = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.tabPageGlobalPermissions = new DevExpress.XtraTab.XtraTabPage();
            this.groupControlGlobalPermissions = new DevExpress.XtraEditors.GroupControl();
            this.chkCanPrintUsers = new DevExpress.XtraEditors.CheckEdit();
            this.chkCanDeleteUsers = new DevExpress.XtraEditors.CheckEdit();
            this.chkCanEditUsers = new DevExpress.XtraEditors.CheckEdit();
            this.chkCanCreateUsers = new DevExpress.XtraEditors.CheckEdit();
            this.panelUserSelectionGlobal = new DevExpress.XtraEditors.PanelControl();
            this.cmbUsersGlobal = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.panelButtons = new DevExpress.XtraEditors.PanelControl();
            this.btnRefresh = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnResetUserPermissions = new DevExpress.XtraEditors.SimpleButton();
            this.btnCopyFromRole = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlMain)).BeginInit();
            this.tabControlMain.SuspendLayout();
            this.tabPageRolePermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRolePermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRolePermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelRoleSelection)).BeginInit();
            this.panelRoleSelection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRoles.Properties)).BeginInit();
            this.tabPageUserPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlUserPermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewUserPermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelUserSelection)).BeginInit();
            this.panelUserSelection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUsers.Properties)).BeginInit();
            this.tabPageGlobalPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlGlobalPermissions)).BeginInit();
            this.groupControlGlobalPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanPrintUsers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanDeleteUsers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanEditUsers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanCreateUsers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelUserSelectionGlobal)).BeginInit();
            this.panelUserSelectionGlobal.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUsersGlobal.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelButtons)).BeginInit();
            this.panelButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControlMain
            // 
            this.tabControlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlMain.Location = new System.Drawing.Point(0, 0);
            this.tabControlMain.Name = "tabControlMain";
            this.tabControlMain.SelectedTabPage = this.tabPageRolePermissions;
            this.tabControlMain.Size = new System.Drawing.Size(1200, 700);
            this.tabControlMain.TabIndex = 0;
            this.tabControlMain.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabPageRolePermissions,
            this.tabPageUserPermissions,
            this.tabPageGlobalPermissions});
            // 
            // tabPageRolePermissions
            // 
            this.tabPageRolePermissions.Controls.Add(this.gridControlRolePermissions);
            this.tabPageRolePermissions.Controls.Add(this.panelRoleSelection);
            this.tabPageRolePermissions.Name = "tabPageRolePermissions";
            this.tabPageRolePermissions.Size = new System.Drawing.Size(1194, 672);
            this.tabPageRolePermissions.Text = "Role Permissions";
            // 
            // gridControlRolePermissions
            // 
            this.gridControlRolePermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRolePermissions.Location = new System.Drawing.Point(0, 50);
            this.gridControlRolePermissions.MainView = this.gridViewRolePermissions;
            this.gridControlRolePermissions.Name = "gridControlRolePermissions";
            this.gridControlRolePermissions.Size = new System.Drawing.Size(1194, 622);
            this.gridControlRolePermissions.TabIndex = 1;
            this.gridControlRolePermissions.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRolePermissions});
            // 
            // gridViewRolePermissions
            // 
            this.gridViewRolePermissions.GridControl = this.gridControlRolePermissions;
            this.gridViewRolePermissions.Name = "gridViewRolePermissions";
            // 
            // panelRoleSelection
            // 
            this.panelRoleSelection.Controls.Add(this.cmbRoles);
            this.panelRoleSelection.Controls.Add(this.labelControl1);
            this.panelRoleSelection.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelRoleSelection.Location = new System.Drawing.Point(0, 0);
            this.panelRoleSelection.Name = "panelRoleSelection";
            this.panelRoleSelection.Size = new System.Drawing.Size(1194, 50);
            this.panelRoleSelection.TabIndex = 0;
            // 
            // cmbRoles
            // 
            this.cmbRoles.Location = new System.Drawing.Point(80, 15);
            this.cmbRoles.Name = "cmbRoles";
            this.cmbRoles.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbRoles.Size = new System.Drawing.Size(300, 20);
            this.cmbRoles.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(15, 18);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(59, 13);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "Select Role:";
            // 
            // tabPageUserPermissions
            // 
            this.tabPageUserPermissions.Controls.Add(this.gridControlUserPermissions);
            this.tabPageUserPermissions.Controls.Add(this.panelUserSelection);
            this.tabPageUserPermissions.Name = "tabPageUserPermissions";
            this.tabPageUserPermissions.Size = new System.Drawing.Size(1194, 672);
            this.tabPageUserPermissions.Text = "User Permissions";
            // 
            // gridControlUserPermissions
            // 
            this.gridControlUserPermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlUserPermissions.Location = new System.Drawing.Point(0, 50);
            this.gridControlUserPermissions.MainView = this.gridViewUserPermissions;
            this.gridControlUserPermissions.Name = "gridControlUserPermissions";
            this.gridControlUserPermissions.Size = new System.Drawing.Size(1194, 622);
            this.gridControlUserPermissions.TabIndex = 1;
            this.gridControlUserPermissions.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewUserPermissions});
            // 
            // gridViewUserPermissions
            // 
            this.gridViewUserPermissions.GridControl = this.gridControlUserPermissions;
            this.gridViewUserPermissions.Name = "gridViewUserPermissions";
            // 
            // panelUserSelection
            // 
            this.panelUserSelection.Controls.Add(this.btnCopyFromRole);
            this.panelUserSelection.Controls.Add(this.btnResetUserPermissions);
            this.panelUserSelection.Controls.Add(this.cmbUsers);
            this.panelUserSelection.Controls.Add(this.labelControl2);
            this.panelUserSelection.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelUserSelection.Location = new System.Drawing.Point(0, 0);
            this.panelUserSelection.Name = "panelUserSelection";
            this.panelUserSelection.Size = new System.Drawing.Size(1194, 50);
            this.panelUserSelection.TabIndex = 0;
            // 
            // cmbUsers
            // 
            this.cmbUsers.Location = new System.Drawing.Point(80, 15);
            this.cmbUsers.Name = "cmbUsers";
            this.cmbUsers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbUsers.Size = new System.Drawing.Size(300, 20);
            this.cmbUsers.TabIndex = 1;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(15, 18);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(59, 13);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "Select User:";
            // 
            // tabPageGlobalPermissions
            // 
            this.tabPageGlobalPermissions.Controls.Add(this.groupControlGlobalPermissions);
            this.tabPageGlobalPermissions.Controls.Add(this.panelUserSelectionGlobal);
            this.tabPageGlobalPermissions.Name = "tabPageGlobalPermissions";
            this.tabPageGlobalPermissions.Size = new System.Drawing.Size(1194, 672);
            this.tabPageGlobalPermissions.Text = "Global Permissions";
            // 
            // groupControlGlobalPermissions
            // 
            this.groupControlGlobalPermissions.Controls.Add(this.chkCanPrintUsers);
            this.groupControlGlobalPermissions.Controls.Add(this.chkCanDeleteUsers);
            this.groupControlGlobalPermissions.Controls.Add(this.chkCanEditUsers);
            this.groupControlGlobalPermissions.Controls.Add(this.chkCanCreateUsers);
            this.groupControlGlobalPermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControlGlobalPermissions.Location = new System.Drawing.Point(0, 50);
            this.groupControlGlobalPermissions.Name = "groupControlGlobalPermissions";
            this.groupControlGlobalPermissions.Size = new System.Drawing.Size(1194, 622);
            this.groupControlGlobalPermissions.TabIndex = 1;
            this.groupControlGlobalPermissions.Text = "User Management Permissions";
            // 
            // chkCanPrintUsers
            // 
            this.chkCanPrintUsers.Location = new System.Drawing.Point(30, 120);
            this.chkCanPrintUsers.Name = "chkCanPrintUsers";
            this.chkCanPrintUsers.Properties.Caption = "Can Print Users";
            this.chkCanPrintUsers.Size = new System.Drawing.Size(200, 19);
            this.chkCanPrintUsers.TabIndex = 3;
            // 
            // chkCanDeleteUsers
            // 
            this.chkCanDeleteUsers.Location = new System.Drawing.Point(30, 95);
            this.chkCanDeleteUsers.Name = "chkCanDeleteUsers";
            this.chkCanDeleteUsers.Properties.Caption = "Can Delete Users";
            this.chkCanDeleteUsers.Size = new System.Drawing.Size(200, 19);
            this.chkCanDeleteUsers.TabIndex = 2;
            // 
            // chkCanEditUsers
            // 
            this.chkCanEditUsers.Location = new System.Drawing.Point(30, 70);
            this.chkCanEditUsers.Name = "chkCanEditUsers";
            this.chkCanEditUsers.Properties.Caption = "Can Edit Users";
            this.chkCanEditUsers.Size = new System.Drawing.Size(200, 19);
            this.chkCanEditUsers.TabIndex = 1;
            // 
            // chkCanCreateUsers
            // 
            this.chkCanCreateUsers.Location = new System.Drawing.Point(30, 45);
            this.chkCanCreateUsers.Name = "chkCanCreateUsers";
            this.chkCanCreateUsers.Properties.Caption = "Can Create Users";
            this.chkCanCreateUsers.Size = new System.Drawing.Size(200, 19);
            this.chkCanCreateUsers.TabIndex = 0;
            //
            // panelUserSelectionGlobal
            //
            this.panelUserSelectionGlobal.Controls.Add(this.cmbUsersGlobal);
            this.panelUserSelectionGlobal.Controls.Add(this.labelControl3);
            this.panelUserSelectionGlobal.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelUserSelectionGlobal.Location = new System.Drawing.Point(0, 0);
            this.panelUserSelectionGlobal.Name = "panelUserSelectionGlobal";
            this.panelUserSelectionGlobal.Size = new System.Drawing.Size(1194, 50);
            this.panelUserSelectionGlobal.TabIndex = 0;
            //
            // cmbUsersGlobal
            //
            this.cmbUsersGlobal.Location = new System.Drawing.Point(80, 15);
            this.cmbUsersGlobal.Name = "cmbUsersGlobal";
            this.cmbUsersGlobal.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbUsersGlobal.Size = new System.Drawing.Size(300, 20);
            this.cmbUsersGlobal.TabIndex = 1;
            //
            // labelControl3
            //
            this.labelControl3.Location = new System.Drawing.Point(15, 18);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(59, 13);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "Select User:";
            // 
            // panelButtons
            // 
            this.panelButtons.Controls.Add(this.btnRefresh);
            this.panelButtons.Controls.Add(this.btnCancel);
            this.panelButtons.Controls.Add(this.btnSave);
            this.panelButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelButtons.Location = new System.Drawing.Point(0, 700);
            this.panelButtons.Name = "panelButtons";
            this.panelButtons.Size = new System.Drawing.Size(1200, 50);
            this.panelButtons.TabIndex = 1;
            // 
            // btnRefresh
            // 
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh.Location = new System.Drawing.Point(950, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(75, 23);
            this.btnRefresh.TabIndex = 2;
            this.btnRefresh.Text = "Refresh";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(1040, 15);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "Cancel";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(1130, 15);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "Save";
            // 
            // btnResetUserPermissions
            // 
            this.btnResetUserPermissions.Location = new System.Drawing.Point(400, 13);
            this.btnResetUserPermissions.Name = "btnResetUserPermissions";
            this.btnResetUserPermissions.Size = new System.Drawing.Size(120, 23);
            this.btnResetUserPermissions.TabIndex = 2;
            this.btnResetUserPermissions.Text = "Reset to Role";
            // 
            // btnCopyFromRole
            // 
            this.btnCopyFromRole.Location = new System.Drawing.Point(540, 13);
            this.btnCopyFromRole.Name = "btnCopyFromRole";
            this.btnCopyFromRole.Size = new System.Drawing.Size(120, 23);
            this.btnCopyFromRole.TabIndex = 3;
            this.btnCopyFromRole.Text = "Copy from Role";
            // 
            // PermissionManagementForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 750);
            this.Controls.Add(this.tabControlMain);
            this.Controls.Add(this.panelButtons);
            this.Name = "PermissionManagementForm";
            this.Text = "Permission Management";
            ((System.ComponentModel.ISupportInitialize)(this.tabControlMain)).EndInit();
            this.tabControlMain.ResumeLayout(false);
            this.tabPageRolePermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRolePermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRolePermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelRoleSelection)).EndInit();
            this.panelRoleSelection.ResumeLayout(false);
            this.panelRoleSelection.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRoles.Properties)).EndInit();
            this.tabPageUserPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlUserPermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewUserPermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelUserSelection)).EndInit();
            this.panelUserSelection.ResumeLayout(false);
            this.panelUserSelection.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUsers.Properties)).EndInit();
            this.tabPageGlobalPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControlGlobalPermissions)).EndInit();
            this.groupControlGlobalPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkCanPrintUsers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanDeleteUsers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanEditUsers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCanCreateUsers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelUserSelectionGlobal)).EndInit();
            this.panelUserSelectionGlobal.ResumeLayout(false);
            this.panelUserSelectionGlobal.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUsersGlobal.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelButtons)).EndInit();
            this.panelButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabControlMain;
        private DevExpress.XtraTab.XtraTabPage tabPageRolePermissions;
        private DevExpress.XtraTab.XtraTabPage tabPageUserPermissions;
        private DevExpress.XtraTab.XtraTabPage tabPageGlobalPermissions;
        private DevExpress.XtraGrid.GridControl gridControlRolePermissions;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRolePermissions;
        private DevExpress.XtraEditors.PanelControl panelRoleSelection;
        private DevExpress.XtraEditors.ComboBoxEdit cmbRoles;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.GridControl gridControlUserPermissions;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewUserPermissions;
        private DevExpress.XtraEditors.PanelControl panelUserSelection;
        private DevExpress.XtraEditors.ComboBoxEdit cmbUsers;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.GroupControl groupControlGlobalPermissions;
        private DevExpress.XtraEditors.CheckEdit chkCanPrintUsers;
        private DevExpress.XtraEditors.CheckEdit chkCanDeleteUsers;
        private DevExpress.XtraEditors.CheckEdit chkCanEditUsers;
        private DevExpress.XtraEditors.CheckEdit chkCanCreateUsers;
        private DevExpress.XtraEditors.PanelControl panelUserSelectionGlobal;
        private DevExpress.XtraEditors.ComboBoxEdit cmbUsersGlobal;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.PanelControl panelButtons;
        private DevExpress.XtraEditors.SimpleButton btnRefresh;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnResetUserPermissions;
        private DevExpress.XtraEditors.SimpleButton btnCopyFromRole;
    }
}
