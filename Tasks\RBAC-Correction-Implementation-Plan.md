# RBAC System Correction Implementation Plan

## Overview
This document provides a comprehensive plan to correct the current RBAC implementation based on specific user requirements. The plan addresses three critical issues and provides detailed implementation steps.

## Part 1: UserMasterForm Global Permissions Correction

### Current Problem
- Incorrectly added separate "Global Permissions" tab (xtraTabPage2)
- Global permissions should be integrated into existing permission tab
- Database schema uses user management focused column names

### Solution

#### Phase 1.1: Database Schema Updates
**File:** `Modules/Procedures/Permissions/RBAC-Schema-Update.sql`

```sql
-- Update global_permissions table structure
ALTER TABLE global_permissions 
RENAME COLUMN can_create_users TO global_new_permission;

ALTER TABLE global_permissions 
RENAME COLUMN can_edit_users TO global_edit_permission;

ALTER TABLE global_permissions 
RENAME COLUMN can_delete_users TO global_delete_permission;

ALTER TABLE global_permissions 
RENAME COLUMN can_print_users TO global_print_permission;

-- Add global read permission column
ALTER TABLE global_permissions 
ADD COLUMN global_read_permission BOOLEAN DEFAULT FALSE;

-- Update column comments
COMMENT ON COLUMN global_permissions.global_read_permission IS 'Global read permission across all forms';
COMMENT ON COLUMN global_permissions.global_new_permission IS 'Global new/create permission across all forms';
COMMENT ON COLUMN global_permissions.global_edit_permission IS 'Global edit permission across all forms';
COMMENT ON COLUMN global_permissions.global_delete_permission IS 'Global delete permission across all forms';
COMMENT ON COLUMN global_permissions.global_print_permission IS 'Global print permission across all forms';
```

#### Phase 1.2: UserMasterForm UI Correction
**Files to Modify:**
- `Forms/MainForms/UserMasterForm.Designer.cs`
- `Forms/MainForms/UserMasterForm.cs`

**UI Changes:**
1. Remove xtraTabPage2 entirely
2. Add GroupControl "Global Permissions" to existing permission tab
3. Add global permission checkboxes with descriptive names:
   - `chkGlobalRead`: "Global Read Access"
   - `chkGlobalNew`: "Global New/Create Access" 
   - `chkGlobalEdit`: "Global Edit Access"
   - `chkGlobalDelete`: "Global Delete Access"
   - `chkGlobalPrint`: "Global Print Access"

**Layout Specification:**
```
[Existing Permission Controls]

[Global Permissions Group]
┌─────────────────────────────────────┐
│ Global Permissions                  │
├─────────────────────────────────────┤
│ □ Global Read Access    □ Global New│
│ □ Global Edit Access    □ Global Del│
│ □ Global Print Access               │
└─────────────────────────────────────┘
```

#### Phase 1.3: Permission Logic Updates
**File:** `Modules/Connections/PermissionDatabaseService.cs`

Add methods for global permission operations:
- `GetGlobalPermissions(int userId)`
- `UpdateGlobalPermissions(GlobalPermissionUpdate update)`
- `HasGlobalPermission(int userId, GlobalPermissionType type)`

## Part 2: PermissionManagementForm Simplification

### Current Problem
- Has 3 tabs including global permissions tab
- Global permissions should only be managed through UserMasterForm

### Solution

#### Phase 2.1: Remove Global Permissions Tab
**Files to Modify:**
- `Forms/MainForms/PermissionManagementForm.Designer.cs`
- `Forms/MainForms/PermissionManagementForm.cs`

**Changes:**
1. Remove `tabPageGlobalPermissions` from tab control
2. Remove all global permission related controls
3. Update tab control to only have 2 tabs
4. Remove global permission methods from code-behind
5. Update save logic to only handle role and user permissions
6. Update tab switching logic

#### Phase 2.2: Update Form Initialization
Remove global permissions related code from:
- `SetupGlobalPermissionsTab()` method
- `LoadGlobalPermissions()` method  
- `SaveGlobalPermissions()` method
- Global permission event handlers

## Part 3: Role Creation and Management System

### Current Problem
- No comprehensive role creation functionality
- Missing role management UI and workflow

### Solution

#### Phase 3.1: Database Service Enhancement
**File:** `Modules/Connections/PermissionDatabaseService.cs`

Add role CRUD methods:
```csharp
public static int CreateRole(RoleCreateRequest request)
public static bool UpdateRole(RoleUpdateRequest request)  
public static bool DeleteRole(int roleId)
public static bool IsRoleInUse(int roleId)
public static List<Role> GetAllRolesWithUserCount()
public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
```

#### Phase 3.2: Role Management Form
**Files to Create:**
- `Forms/MainForms/RoleManagementForm.cs`
- `Forms/MainForms/RoleManagementForm.Designer.cs`
- `Forms/MainForms/RoleManagementForm.resx`

**Features:**
- MDI child form with ribbon interface
- Grid showing: Role Name, Description, Active Status, Created Date, User Count
- Ribbon buttons: New Role, Edit Role, Delete Role, Copy Permissions, Refresh
- Double-click to edit role
- Context menu with operations

#### Phase 3.3: Role Create/Edit Dialog
**Files to Create:**
- `Forms/Dialogs/RoleCreateEditDialog.cs`
- `Forms/Dialogs/RoleCreateEditDialog.Designer.cs`
- `Forms/Dialogs/RoleCreateEditDialog.resx`

**Features:**
- Modal dialog (400x300)
- Fields: Role Name (required), Description (optional), Active checkbox
- Validation: Role name unique, not empty
- Save/Cancel buttons

#### Phase 3.4: Role Management Helper
**File to Create:**
- `Modules/Helpers/RoleManagement/RoleManagementHelper.cs`

**Features:**
- Role validation logic
- Permission copying utilities
- Role deletion validation
- User count calculations

## Implementation Sequence

### Phase 1: Database Updates (30 minutes)
1. Create and execute RBAC-Schema-Update.sql
2. Update PermissionDatabaseService with new methods
3. Update data models for global permissions

### Phase 2: UserMasterForm Correction (1.5 hours)
1. Remove xtraTabPage2 from designer
2. Add global permission controls to existing tab
3. Update code-behind for global permission handling
4. Test global permission loading/saving

### Phase 3: PermissionManagementForm Simplification (1 hour)
1. Remove global permissions tab from designer
2. Remove global permission code from form
3. Update tab control logic
4. Test role and user permission functionality

### Phase 4: Role Creation System (3 hours)
1. Create RoleManagementForm with grid and ribbon
2. Create RoleCreateEditDialog with validation
3. Add database service methods
4. Create helper classes
5. Update .csproj file
6. Test complete role management workflow

### Phase 5: Integration and Testing (1 hour)
1. Update global permission checking logic
2. Test global permission filtering in MainFrame
3. Validate role creation workflow
4. Update documentation

## File Structure Summary

### New Files to Create:
```
Modules/Procedures/Permissions/RBAC-Schema-Update.sql
Forms/MainForms/RoleManagementForm.cs
Forms/MainForms/RoleManagementForm.Designer.cs
Forms/MainForms/RoleManagementForm.resx
Forms/Dialogs/RoleCreateEditDialog.cs
Forms/Dialogs/RoleCreateEditDialog.Designer.cs
Forms/Dialogs/RoleCreateEditDialog.resx
Modules/Helpers/RoleManagement/RoleManagementHelper.cs
```

### Files to Modify:
```
Forms/MainForms/UserMasterForm.Designer.cs
Forms/MainForms/UserMasterForm.cs
Forms/MainForms/PermissionManagementForm.Designer.cs
Forms/MainForms/PermissionManagementForm.cs
Modules/Connections/PermissionDatabaseService.cs
Modules/Models/PermissionModels.cs
ProManage.csproj
Tasks/project-tasks-tracker.md
```

## Validation Criteria

### Part 1 Validation:
- [ ] UserMasterForm has only one permission tab
- [ ] Global permission controls integrated into existing tab
- [ ] Global permissions control ribbon button access
- [ ] Database schema updated with correct column names

### Part 2 Validation:
- [ ] PermissionManagementForm has only 2 tabs
- [ ] No global permission functionality in this form
- [ ] Role and user permission functionality works correctly

### Part 3 Validation:
- [ ] Role creation dialog works with validation
- [ ] Role management grid shows all roles with user counts
- [ ] Role CRUD operations work correctly
- [ ] Permission copying between roles works
- [ ] Role deletion prevents deletion of roles in use

## Next Steps

1. Execute database schema updates
2. Implement UserMasterForm corrections
3. Simplify PermissionManagementForm
4. Create role management system
5. Test complete workflow
6. Update project documentation

This plan provides a comprehensive solution to all three identified issues while maintaining the existing functionality and following ProManage development patterns.
