# RBAC System UI Design Specifications

## Overview
This document provides detailed UI design specifications for the corrected RBAC system, including exact layouts, control specifications, and user interaction workflows.

## Part 1: UserMasterForm Enhanced Permission Tab

### Current State
- Has 3 tabs: General, Action Permissions (INCORRECT), System Preferences
- Action Permissions tab contains global permission checkboxes (WRONG LOCATION)

### Corrected Design

#### Tab Structure (CORRECTED)
```
┌─────────────────────────────────────────────────────────┐
│ [General] [Permissions] [System Preferences]           │
└─────────────────────────────────────────────────────────┘
```

#### Permissions Tab Layout
```
┌─────────────────────────────────────────────────────────┐
│ Permissions Tab                                         │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ ┌─ Role Assignment ─────────────────────────────────┐   │
│ │ Role: [Administrator ▼]                          │   │
│ │ □ Override role permissions for this user        │   │
│ └─────────────────────────────────────────────────────┘   │
│                                                         │
│ ┌─ Form Permissions ────────────────────────────────┐   │
│ │ [Permission Grid showing effective permissions]   │   │
│ │ Form Name    │ Read │ New │ Edit │ Delete │ Print │   │
│ │ UserMaster   │  ✓   │  ✓  │  ✓   │   ✓    │   ✓   │   │
│ │ RoleMaster   │  ✓   │  ✓  │  ✓   │   ✓    │   ✓   │   │
│ │ ...          │  ... │ ... │ ...  │  ...   │  ...  │   │
│ └─────────────────────────────────────────────────────┘   │
│                                                         │
│ ┌─ Global Permissions ──────────────────────────────┐   │
│ │ These permissions control access to ribbon        │   │
│ │ buttons across ALL forms in the application       │   │
│ │                                                   │   │
│ │ □ Global Read Access     □ Global New Access      │   │
│ │ □ Global Edit Access     □ Global Delete Access   │   │
│ │ □ Global Print Access                             │   │
│ └─────────────────────────────────────────────────────┘   │
│                                                         │
│ [Manage Permissions...] [Reset to Role Defaults]       │
└─────────────────────────────────────────────────────────┘
```

#### Control Specifications

**Global Permission Controls:**
```csharp
// Group Control
private DevExpress.XtraEditors.GroupControl groupGlobalPermissions;

// Checkboxes
private DevExpress.XtraEditors.CheckEdit chkGlobalRead;
private DevExpress.XtraEditors.CheckEdit chkGlobalNew;
private DevExpress.XtraEditors.CheckEdit chkGlobalEdit;
private DevExpress.XtraEditors.CheckEdit chkGlobalDelete;
private DevExpress.XtraEditors.CheckEdit chkGlobalPrint;
```

**Layout Properties:**
- GroupControl Location: (20, 350)
- GroupControl Size: (650, 120)
- Checkbox Size: (150, 19)
- Checkbox Spacing: 10px vertical, 20px horizontal

**Text Properties:**
- chkGlobalRead.Text = "Global Read Access"
- chkGlobalNew.Text = "Global New Access"
- chkGlobalEdit.Text = "Global Edit Access"
- chkGlobalDelete.Text = "Global Delete Access"
- chkGlobalPrint.Text = "Global Print Access"

## Part 2: PermissionManagementForm Simplified Design

### Current State (INCORRECT)
- 3 tabs: Role Permissions, User Permissions, Global Permissions

### Corrected Design
```
┌─────────────────────────────────────────────────────────┐
│ Permission Management                                   │
├─────────────────────────────────────────────────────────┤
│ [Role Permissions] [User Permissions]                  │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ Role Permissions Tab:                                   │
│ ┌─ Role Selection ──────────────────────────────────┐   │
│ │ Role: [Administrator ▼]                          │   │
│ └─────────────────────────────────────────────────────┘   │
│                                                         │
│ ┌─ Role Permissions Grid ───────────────────────────┐   │
│ │ Form Name      │ Read │ New │ Edit │ Delete │ Print│   │
│ │ UserMaster     │  ✓   │  ✓  │  ✓   │   ✓    │   ✓  │   │
│ │ RoleMaster     │  ✓   │  ✓  │  ✓   │   ✓    │   ✓  │   │
│ │ DatabaseForm   │  ✓   │  ✓  │  ✓   │   ✓    │   ✓  │   │
│ │ ...            │  ... │ ... │ ...  │  ...   │ ... │   │
│ └─────────────────────────────────────────────────────┘   │
│                                                         │
│ [Copy from Role...] [Reset All] [Save] [Cancel]        │
└─────────────────────────────────────────────────────────┘
```

### Changes Required
1. Remove `tabPageGlobalPermissions` from TabControl
2. Update TabControl.TabPages collection to only include 2 tabs
3. Remove all global permission related controls and methods
4. Update tab switching logic to handle 2 tabs only

## Part 3: Role Management System Design

### RoleManagementForm Layout
```
┌─────────────────────────────────────────────────────────┐
│ Role Management                                         │
├─ Ribbon ────────────────────────────────────────────────┤
│ [New Role] [Edit Role] [Delete Role] [Copy Permissions] │
│ [Refresh]                                               │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ ┌─ Roles Grid ──────────────────────────────────────┐   │
│ │ Role Name    │ Description      │ Active │ Users │   │
│ │ Administrator│ Full system acc..│   ✓    │   3   │   │
│ │ Manager      │ Management level │   ✓    │   5   │   │
│ │ User         │ Standard user    │   ✓    │  12   │   │
│ │ ReadOnly     │ Read-only access │   ✓    │   2   │   │
│ │ CustomRole   │ Custom role desc │   ✓    │   0   │   │
│ └─────────────────────────────────────────────────────┘   │
│                                                         │
│ Status: 5 roles loaded, 22 total users                 │
└─────────────────────────────────────────────────────────┘
```

#### Grid Specifications
```csharp
// Grid Control
private DevExpress.XtraGrid.GridControl gridControlRoles;
private DevExpress.XtraGrid.Views.Grid.GridView gridViewRoles;

// Columns
private DevExpress.XtraGrid.Columns.GridColumn colRoleName;
private DevExpress.XtraGrid.Columns.GridColumn colDescription;
private DevExpress.XtraGrid.Columns.GridColumn colIsActive;
private DevExpress.XtraGrid.Columns.GridColumn colUserCount;
private DevExpress.XtraGrid.Columns.GridColumn colCreatedDate;
```

**Column Properties:**
- colRoleName: Width=150, Caption="Role Name"
- colDescription: Width=250, Caption="Description"
- colIsActive: Width=80, Caption="Active", ColumnType=CheckBox
- colUserCount: Width=80, Caption="Users", Alignment=Center
- colCreatedDate: Width=120, Caption="Created", Format=Short Date

### RoleCreateEditDialog Layout
```
┌─────────────────────────────────────┐
│ Create New Role                     │
├─────────────────────────────────────┤
│                                     │
│ Role Name: [________________]       │
│                                     │
│ Description:                        │
│ ┌─────────────────────────────────┐ │
│ │                                 │ │
│ │                                 │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ □ Active Role                       │
│                                     │
│ ┌─ Initial Permissions ───────────┐ │
│ │ ○ No permissions (recommended)  │ │
│ │ ○ Copy from existing role:      │ │
│ │   [Select Role ▼]               │ │
│ └─────────────────────────────────┘ │
│                                     │
│           [Save] [Cancel]           │
└─────────────────────────────────────┘
```

#### Dialog Specifications
- Size: 450x350
- FormBorderStyle: FixedDialog
- MaximizeBox: false
- MinimizeBox: false
- StartPosition: CenterParent

**Controls:**
```csharp
private DevExpress.XtraEditors.TextEdit txtRoleName;
private DevExpress.XtraEditors.MemoEdit txtDescription;
private DevExpress.XtraEditors.CheckEdit chkIsActive;
private DevExpress.XtraEditors.RadioGroup rgInitialPermissions;
private DevExpress.XtraEditors.ComboBoxEdit cmbCopyFromRole;
private DevExpress.XtraEditors.SimpleButton btnSave;
private DevExpress.XtraEditors.SimpleButton btnCancel;
```

## Permission Logic Flow

### Global Permission Checking
```
User attempts action (e.g., click New button)
    ↓
Check Global Permission (e.g., Global New Access)
    ↓
If Global Permission = FALSE → Deny access
    ↓
If Global Permission = TRUE → Check Form-specific Permission
    ↓
Check Form Permission (e.g., UserMaster New Permission)
    ↓
If Form Permission = FALSE → Deny access
    ↓
If Form Permission = TRUE → Allow access
```

### Permission Precedence
1. **Global Permissions** (First-level filter)
   - Controls ribbon button visibility/enablement
   - Applied across ALL forms
   
2. **Form-specific Permissions** (Second-level filter)
   - Controls access to specific form operations
   - User permissions override role permissions

### UI Feedback
- **Disabled buttons**: Gray out ribbon buttons when global permission denied
- **Hidden buttons**: Hide ribbon buttons when read permission denied
- **Tooltips**: Show permission status on hover
- **Status indicators**: Show permission source (role vs override) with color coding

## Implementation Notes

### Color Coding Scheme
- **Green**: Permission granted by role
- **Blue**: Permission granted by user override
- **Red**: Permission denied by user override
- **Gray**: Permission inherited from role (no override)

### Validation Rules
1. Role names must be unique (case-insensitive)
2. Role names cannot be empty or whitespace
3. Cannot delete roles that have users assigned
4. Cannot delete system roles (Administrator, Manager, User, ReadOnly)
5. Global permissions require confirmation when changing for current user

### Error Handling
- Show user-friendly error messages for validation failures
- Prevent system lockout scenarios
- Graceful degradation when permission service unavailable
- Audit trail for permission changes

This design specification ensures a consistent, user-friendly interface that properly implements the corrected RBAC requirements while maintaining ProManage's design standards.
